from django.contrib.auth.models import User
from django.contrib.gis import forms
from django.forms.widgets import Widget
from django.template.loader import render_to_string
from django.utils.safestring import mark_safe
from leaflet.forms.widgets import LeafletWidget

from mapping.models import HubsGroupEditor


class HubForm(forms.ModelForm):
    polygon = forms.PolygonField(
        widget=forms.OSMWidget(
            attrs={
                'map_width': 800, 'map_height': 500,
                'default_lat': 43.257109, 'default_lon': 76.946314
            }
        ), label='Полигон', required=False
    )


class HubsGroupEditorForm(forms.ModelForm):
    class Meta:
        model = HubsGroupEditor
        fields = ['name']

