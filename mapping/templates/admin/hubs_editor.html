{% extends "admin/base_site.html" %}
{% load static %}

{% block title %}{{ title }} | {{ site_title|default:"Django site admin" }}{% endblock %}

{% block extrahead %}
    {{ block.super }}
    <link rel="stylesheet" type="text/css" href="{% static 'map/css/leaflet.css' %}">
    <link rel="stylesheet" type="text/css" href="{% static 'map/css/Control.Geocoder.css' %}"/>
    <link rel="stylesheet" href="{% static 'map/css/leaflet.draw.css' %}">

    <script type="text/javascript" src="{% static 'map/js/leaflet.js' %}"></script>
    <script type="text/javascript" src="{% static 'map/js/jquery-3.7.0.min.js' %}"></script>
    <script src="{% static 'map/js/leaflet.draw.js' %}"></script>
    <script src="{% static 'map/js/turf.min.js' %}"></script>
    <script src="{% static 'map/js/Control.Geocoder.js' %}"></script>

    <style>
        .hubs-editor-container {
            background-color: #ffffff;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 20px;
            margin: 20px 0;
        }

        .controls-section {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .controls-section h3 {
            margin-top: 0;
            color: #495057;
            font-size: 16px;
            font-weight: bold;
        }

        #hubs-form {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        #hubs-form label {
            font-weight: bold;
            margin-right: 5px;
        }

        #layer-select, #region-select {
            padding: 6px 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 13px;
            min-width: 150px;
        }

        .admin-btn {
            padding: 8px 15px;
            font-size: 13px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            font-weight: normal;
        }

        .admin-btn-primary {
            background-color: #417690;
            color: white;
        }

        .admin-btn-primary:hover {
            background-color: #205067;
            color: white;
        }

        .admin-btn-success {
            background-color: #28a745;
            color: white;
        }

        .admin-btn-success:hover {
            background-color: #1e7e34;
            color: white;
        }

        .admin-btn-warning {
            background-color: #ffc107;
            color: #212529;
        }

        .admin-btn-warning:hover {
            background-color: #e0a800;
        }

        .admin-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        #hubs-map {
            height: 500px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 15px 0;
        }

        .status-info {
            margin-top: 15px;
            padding: 10px;
            background-color: #e9ecef;
            border-radius: 4px;
            font-size: 13px;
        }

        .status-info strong {
            color: #495057;
        }

        .help-text {
            margin-top: 10px;
            font-size: 12px;
            color: #6c757d;
        }

        .help-text ul {
            margin: 5px 0;
            padding-left: 20px;
        }

        .checkbox-container {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .breadcrumbs {
            background: #417690;
            color: #fff;
            padding: 10px 20px;
            margin: 0;
        }

        .breadcrumbs a {
            color: #cce7f0;
            text-decoration: none;
        }

        .breadcrumbs a:hover {
            color: white;
        }
    </style>
{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
    <a href="{% url 'admin:index' %}">Главная</a>
    &rsaquo; {{ title }}
</div>
{% endblock %}

{% block content %}
<h1>{{ title }}</h1>

<div class="hubs-editor-container">
    {% csrf_token %}
    
    <div class="controls-section">
        <h3>Параметры загрузки</h3>
        <form id="hubs-form">
            <label for="layer-select">Слой:</label>
            <select id="layer-select">
                <option value="">Выберите слой</option>
                {% for layer in layers %}
                <option value="{{ layer.id }}">{{ layer.name }}</option>
                {% endfor %}
            </select>

            <label for="region-select">Регион:</label>
            <select id="region-select">
                <option value="">Выберите регион</option>
                {% for region in regions %}
                <option value="{{ region.id }}">{{ region.name }}</option>
                {% endfor %}
            </select>

            <div class="checkbox-container">
                <input type="checkbox" id="show-polygons" name="show-polygons">
                <label for="show-polygons">Только полигоны</label>
            </div>

            <button id="load-hubs-button" type="button" class="admin-btn admin-btn-primary">Загрузить хабы</button>
        </form>
    </div>

    <div class="controls-section">
        <h3>Действия с хабами</h3>
        <div style="display: flex; gap: 10px; flex-wrap: wrap;">
            <button id="save-hubs-button" type="button" class="admin-btn admin-btn-success" disabled>Сохранить изменения</button>
            <button id="reset-hubs-button" type="button" class="admin-btn admin-btn-warning" disabled>Отменить изменения</button>
        </div>
        
        <div class="help-text">
            <strong>Инструкция:</strong>
            <ul>
                <li>Выберите слой и регион, затем нажмите "Загрузить хабы"</li>
                <li>Используйте инструменты редактирования для изменения полигонов</li>
                <li>Нажмите "Сохранить изменения" для применения</li>
                <li>Синие полигоны - активные отделения, красные - неактивные</li>
            </ul>
        </div>
    </div>

    <div id="hubs-map"></div>
    
    <div class="status-info">
        <div id="hubs-status"><strong>Статус:</strong> Выберите параметры и загрузите хабы</div>
    </div>
</div>

<script>
    django.jQuery(document).ready(function () {
        var $ = django.jQuery;
        var map = null;
        var drawnItems = null;
        var hubsData = [];
        var departments = [];
        var originalHubsData = [];
        var hasChanges = false;

        // Инициализация карты
        function initMap() {
            if (map) {
                map.remove();
            }
            
            map = L.map('hubs-map').setView([43.257109, 76.946314], 6);
            
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '<a href="https://www.kazpost.kz/kk">KAZPOST</a> contributors'
            }).addTo(map);

            drawnItems = new L.FeatureGroup();
            map.addLayer(drawnItems);

            var drawControl = new L.Control.Draw({
                draw: {
                    polygon: false,
                    polyline: false,
                    rectangle: false,
                    circle: false,
                    marker: false,
                },
                edit: {
                    featureGroup: drawnItems,
                    edit: true,
                    remove: false
                },
            });
            map.addControl(drawControl);

            // Обработчик изменений
            map.on(L.Draw.Event.EDITED, function (event) {
                hasChanges = true;
                updateButtonStates();
                $('#hubs-status').html('<strong>Статус:</strong> <span style="color: orange;">Есть несохраненные изменения</span>');
            });
        }

        // Обновление состояния кнопок
        function updateButtonStates() {
            $('#save-hubs-button').prop('disabled', !hasChanges);
            $('#reset-hubs-button').prop('disabled', !hasChanges);
        }

        // Загрузка отделений для региона
        function loadDepartmentsForRegion(selectedRegion, callback) {
            $.getJSON('{{ KAZPOSTGEO_PREFIX }}/get_departments/?region_id=' + selectedRegion, function (data) {
                departments = data;
                if (callback) callback();
            });
        }

        // Отображение отделений на карте
        function displayDepartments() {
            departments.forEach(function (point) {
                var marker = L.marker([point.lat, point.lon]);
                marker.bindPopup(point.name);
                marker.addTo(map);
            });
        }

        // Загрузка хабов
        function loadHubs() {
            var selectedLayer = $('#layer-select').val();
            var selectedRegion = $('#region-select').val();
            var showPolygonsOnly = $('#show-polygons').is(':checked');

            if (!selectedLayer || !selectedRegion) {
                alert('Выберите слой и регион');
                return;
            }

            $('#hubs-status').html('<strong>Статус:</strong> Загрузка...');

            // Очистка карты
            map.eachLayer(function (layer) {
                if (layer instanceof L.Polygon || layer instanceof L.Marker) {
                    map.removeLayer(layer);
                }
            });
            drawnItems.clearLayers();

            // Загрузка отделений
            if (!showPolygonsOnly) {
                loadDepartmentsForRegion(selectedRegion, displayDepartments);
            }

            // Загрузка полигонов хабов
            var url = '{{ KAZPOSTGEO_PREFIX }}/get_layer_polygons/?layer_id=' + selectedLayer + '&region_id=' + selectedRegion;

            $.getJSON(url, function (data) {
                hubsData = data.polygons;
                originalHubsData = JSON.parse(JSON.stringify(data.polygons));

                data.polygons.forEach(function (polygon) {
                    var coords = polygon.geometry.coordinates[0];
                    var latLngs = coords.map(function (coord) {
                        return [coord[1], coord[0]];
                    });

                    var fillColor = polygon.status !== 1 ? 'red' : 'blue';

                    var polygonLayer = L.polygon(latLngs, {
                        fillColor: fillColor,
                        interactive: true,
                        weight: 2,
                        opacity: 0.8,
                        fillOpacity: 0.3
                    });

                    polygonLayer.hubId = polygon.id;
                    polygonLayer.originalData = polygon;

                    polygonLayer.bindTooltip(polygon.departament + ' (ID: ' + polygon.id + ')', {
                        permanent: false,
                        direction: 'auto',
                    });

                    drawnItems.addLayer(polygonLayer);
                });

                if (data.region_lat && data.region_lon) {
                    map.setView([data.region_lat, data.region_lon], 10);
                }

                hasChanges = false;
                updateButtonStates();
                $('#hubs-status').html('<strong>Статус:</strong> Загружено хабов: ' + data.polygons.length);
            }).fail(function() {
                $('#hubs-status').html('<strong>Статус:</strong> <span style="color: red;">Ошибка загрузки</span>');
            });
        }

        // Сохранение изменений
        function saveHubs() {
            if (!hasChanges) {
                alert('Нет изменений для сохранения');
                return;
            }

            var changedHubs = [];

            drawnItems.eachLayer(function(layer) {
                if (layer instanceof L.Polygon && layer.hubId) {
                    var geoJson = layer.toGeoJSON();
                    changedHubs.push({
                        id: layer.hubId,
                        geometry: geoJson.geometry
                    });
                }
            });

            if (changedHubs.length === 0) {
                alert('Нет полигонов для сохранения');
                return;
            }

            $('#hubs-status').html('<strong>Статус:</strong> Сохранение...');

            // TODO: Здесь будет AJAX запрос для массового сохранения
            console.log('Сохраняем хабы:', changedHubs);

            // Временная имитация сохранения
            setTimeout(function() {
                hasChanges = false;
                updateButtonStates();
                $('#hubs-status').html('<strong>Статус:</strong> <span style="color: green;">Сохранено хабов: ' + changedHubs.length + '</span>');
                alert('Изменения сохранены! (пока только в консоли)');
            }, 1000);
        }

        // Отмена изменений
        function resetHubs() {
            if (!hasChanges) {
                return;
            }

            if (!confirm('Отменить все несохраненные изменения?')) {
                return;
            }

            // Очистка и перезагрузка оригинальных данных
            drawnItems.clearLayers();

            originalHubsData.forEach(function (polygon) {
                var coords = polygon.geometry.coordinates[0];
                var latLngs = coords.map(function (coord) {
                    return [coord[1], coord[0]];
                });

                var fillColor = polygon.status !== 1 ? 'red' : 'blue';

                var polygonLayer = L.polygon(latLngs, {
                    fillColor: fillColor,
                    interactive: true,
                    weight: 2,
                    opacity: 0.8,
                    fillOpacity: 0.3
                });

                polygonLayer.hubId = polygon.id;
                polygonLayer.originalData = polygon;

                polygonLayer.bindTooltip(polygon.departament + ' (ID: ' + polygon.id + ')', {
                    permanent: false,
                    direction: 'auto',
                });

                drawnItems.addLayer(polygonLayer);
            });

            hasChanges = false;
            updateButtonStates();
            $('#hubs-status').html('<strong>Статус:</strong> Изменения отменены');
        }

        // Обработчики событий
        $('#load-hubs-button').on('click', loadHubs);
        $('#save-hubs-button').on('click', saveHubs);
        $('#reset-hubs-button').on('click', resetHubs);

        // Инициализация
        initMap();
    });
</script>
{% endblock %}
